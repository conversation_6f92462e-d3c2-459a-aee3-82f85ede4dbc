<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel工具软件设计说明文档</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 25px;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
        }
        .download-btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 10px;
            font-size: 12px;
        }
        .download-btn:hover {
            background-color: #2980b9;
        }
        .download-btn.svg {
            background-color: #27ae60;
            margin-left: 5px;
        }
        .download-btn.svg:hover {
            background-color: #229954;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .code-block {
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        .feature-list {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .tech-item {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            text-align: center;
        }
        .version-info {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Excel工具软件设计说明文档</h1>
        
        <div class="version-info">
            <strong>软件名称：</strong>Excel工具软件<br>
            <strong>版本号：</strong>v1.0.0<br>
            <strong>开发语言：</strong>Python 3.7+<br>
            <strong>文档版本：</strong>1.0<br>
            <strong>创建日期：</strong>2025年1月<br>
        </div>

        <h2>1. 项目概述</h2>
        <p>Excel工具软件是一款专为局域网环境设计的Excel数据处理工具，无需连接外网即可运行。软件采用Python语言开发，基于Tkinter图形界面框架，提供了列对比、数据去重、格式调整等核心功能，帮助用户轻松完成各类Excel数据处理任务。</p>
        
        <div class="feature-list">
            <h3>核心特性：</h3>
            <ul>
                <li>🔍 <strong>列对比功能：</strong>对比两个Excel文件指定列的差异，差异项标红显示</li>
                <li>🔄 <strong>数据去重：</strong>按指定列去重，支持保留第一条或最后一条记录</li>
                <li>📝 <strong>格式调整：</strong>统一日期格式、数字精度、文本格式</li>
                <li>💻 <strong>离线运行：</strong>无需网络连接，适合局域网环境</li>
                <li>🎯 <strong>简单易用：</strong>图形化界面，操作简便</li>
                <li>⚡ <strong>高效处理：</strong>多线程处理，支持大文件</li>
            </ul>
        </div>

        <h2>2. 系统架构设计</h2>
        <h3>2.1 总体架构</h3>
        <div class="mermaid" id="architecture">
            graph TB
                A[用户界面层<br/>Tkinter GUI] --> B[业务逻辑层<br/>ExcelTool Class]
                B --> C[数据处理层<br/>Pandas + OpenPyXL]
                C --> D[文件系统层<br/>Excel Files]
                
                B --> E[多线程处理<br/>Threading]
                B --> F[状态管理<br/>Progress & Status]
                
                G[命令行接口<br/>CLI Module] --> B
                
                style A fill:#e1f5fe
                style B fill:#f3e5f5
                style C fill:#e8f5e8
                style D fill:#fff3e0
                style E fill:#fce4ec
                style F fill:#f1f8e9
                style G fill:#e0f2f1
        </div>
        <button class="download-btn" onclick="downloadDiagram('architecture')">下载PNG</button>
        <button class="download-btn svg" onclick="downloadSvg('architecture')">下载SVG</button>

        <h3>2.2 模块结构</h3>
        <div class="mermaid" id="modules">
            graph LR
                A[main.py<br/>主程序模块] --> B[GUI界面管理]
                A --> C[文件处理逻辑]
                A --> D[数据对比算法]
                A --> E[去重处理算法]
                A --> F[格式调整算法]
                
                G[run.py<br/>启动模块] --> A
                H[cli.py<br/>命令行模块] --> C
                H --> D
                H --> E
                H --> F
                
                I[create_sample_data.py<br/>示例数据生成] --> J[Excel文件生成]
                
                style A fill:#ffeb3b
                style G fill:#4caf50
                style H fill:#2196f3
                style I fill:#ff9800
        </div>
        <button class="download-btn" onclick="downloadDiagram('modules')">下载PNG</button>
        <button class="download-btn svg" onclick="downloadSvg('modules')">下载SVG</button>

        <h2>3. 功能模块设计</h2>
        <h3>3.1 功能模块关系图</h3>
        <div class="mermaid" id="functions">
            graph TD
                A[Excel工具软件] --> B[列对比功能]
                A --> C[数据去重功能]
                A --> D[格式调整功能]
                
                B --> B1[文件选择]
                B --> B2[列选择]
                B --> B3[差异对比]
                B --> B4[结果标记]
                B --> B5[结果保存]
                
                C --> C1[文件加载]
                C --> C2[列选择]
                C --> C3[去重策略]
                C --> C4[重复检测]
                C --> C5[结果统计]
                
                D --> D1[文件选择]
                D --> D2[格式类型选择]
                D --> D3[格式转换]
                D --> D4[结果预览]
                D --> D5[格式保存]
                
                style A fill:#e91e63
                style B fill:#2196f3
                style C fill:#4caf50
                style D fill:#ff9800
        </div>
        <button class="download-btn" onclick="downloadDiagram('functions')">下载PNG</button>
        <button class="download-btn svg" onclick="downloadSvg('functions')">下载SVG</button>

        <h3>3.2 数据流程图</h3>
        <div class="mermaid" id="dataflow">
            flowchart TD
                A[用户选择Excel文件] --> B[文件格式验证]
                B --> C{文件有效?}
                C -->|是| D[加载数据到DataFrame]
                C -->|否| E[显示错误信息]
                
                D --> F[用户选择处理功能]
                F --> G{功能类型}
                
                G -->|列对比| H[选择对比列]
                G -->|数据去重| I[选择去重列和策略]
                G -->|格式调整| J[选择格式类型]
                
                H --> K[执行对比算法]
                I --> L[执行去重算法]
                J --> M[执行格式转换]
                
                K --> N[标记差异数据]
                L --> O[统计去重结果]
                M --> P[应用新格式]
                
                N --> Q[生成结果文件]
                O --> Q
                P --> Q
                
                Q --> R[保存Excel文件]
                R --> S[显示完成信息]
                
                style A fill:#e3f2fd
                style Q fill:#c8e6c9
                style S fill:#dcedc8
        </div>
        <button class="download-btn" onclick="downloadDiagram('dataflow')">下载PNG</button>
        <button class="download-btn svg" onclick="downloadSvg('dataflow')">下载SVG</button>

        <h2>4. 技术实现方案</h2>
        <h3>4.1 技术栈</h3>
        <div class="tech-stack">
            <div class="tech-item">
                <h4>编程语言</h4>
                <p>Python 3.7+</p>
            </div>
            <div class="tech-item">
                <h4>GUI框架</h4>
                <p>Tkinter</p>
            </div>
            <div class="tech-item">
                <h4>数据处理</h4>
                <p>Pandas</p>
            </div>
            <div class="tech-item">
                <h4>Excel处理</h4>
                <p>OpenPyXL</p>
            </div>
            <div class="tech-item">
                <h4>多线程</h4>
                <p>Threading</p>
            </div>
            <div class="tech-item">
                <h4>命令行</h4>
                <p>Argparse</p>
            </div>
        </div>

        <h3>4.2 核心算法设计</h3>
        
        <h4>4.2.1 列对比算法</h4>
        <div class="code-block">
算法流程：
1. 读取两个Excel文件的指定列数据
2. 转换为集合进行差集运算
3. 计算 only_in_a = set_a - set_b
4. 计算 only_in_b = set_b - set_a  
5. 在原数据中标记差异项
6. 使用红色背景标记差异单元格
7. 保存带样式的Excel文件

时间复杂度：O(n + m)，其中n、m为两个文件的数据量
空间复杂度：O(n + m)
        </div>

        <h4>4.2.2 数据去重算法</h4>
        <div class="code-block">
算法流程：
1. 基于Pandas的drop_duplicates方法
2. 支持keep='first'和keep='last'策略
3. 统计原始数据量和去重后数据量
4. 计算去重率 = (原始量 - 去重后量) / 原始量 * 100%

时间复杂度：O(n log n)，基于排序的去重算法
空间复杂度：O(n)
        </div>

        <h4>4.2.3 格式调整算法</h4>
        <div class="code-block">
格式类型处理：
- 日期格式：使用pd.to_datetime()转换为统一的YYYY-MM-DD格式
- 数字格式：使用round()函数保留两位小数
- 文本格式：使用strip()方法去除首尾空格

错误处理：对无法转换的数据保持原样，记录错误信息
        </div>

        <h2>5. 界面设计</h2>
        <h3>5.1 界面布局结构</h3>
        <div class="mermaid" id="ui-layout">
            graph TD
                A[主窗口 1000x700] --> B[标题区域]
                A --> C[功能标签页 Notebook]
                A --> D[状态栏]

                C --> E[列对比标签页]
                C --> F[数据去重标签页]
                C --> G[格式调整标签页]

                E --> E1[文件上传区域]
                E --> E2[列选择区域]
                E --> E3[操作按钮区域]
                E --> E4[结果显示区域]

                F --> F1[文件选择区域]
                F --> F2[去重配置区域]
                F --> F3[操作按钮区域]
                F --> F4[统计结果区域]

                G --> G1[文件选择区域]
                G --> G2[格式配置区域]
                G --> G3[操作按钮区域]
                G --> G4[预览区域]

                style A fill:#f8f9fa
                style C fill:#e9ecef
                style E fill:#e3f2fd
                style F fill:#e8f5e8
                style G fill:#fff3e0
        </div>
        <button class="download-btn" onclick="downloadDiagram('ui-layout')">下载PNG</button>
        <button class="download-btn svg" onclick="downloadSvg('ui-layout')">下载SVG</button>

        <h3>5.2 用户交互流程</h3>
        <div class="mermaid" id="user-flow">
            sequenceDiagram
                participant U as 用户
                participant G as GUI界面
                participant L as 业务逻辑
                participant F as 文件系统

                U->>G: 选择功能标签页
                U->>G: 点击"选择文件"
                G->>F: 打开文件对话框
                F-->>G: 返回文件路径
                G->>U: 显示文件路径

                U->>G: 点击"加载文件"
                G->>L: 调用文件加载方法
                L->>F: 读取Excel文件
                F-->>L: 返回数据
                L-->>G: 更新列选择下拉框
                G->>U: 显示可选列

                U->>G: 选择处理参数
                U->>G: 点击"开始处理"
                G->>L: 启动处理线程
                L->>L: 执行数据处理
                L-->>G: 更新进度条
                L-->>G: 返回处理结果
                G->>U: 显示处理完成

                U->>G: 点击"保存结果"
                G->>F: 保存Excel文件
                F-->>G: 保存完成
                G->>U: 显示保存成功
        </div>
        <button class="download-btn" onclick="downloadDiagram('user-flow')">下载PNG</button>
        <button class="download-btn svg" onclick="downloadSvg('user-flow')">下载SVG</button>

        <h2>6. 数据库设计</h2>
        <p>本软件为单机版Excel处理工具，不涉及数据库设计。所有数据处理均在内存中完成，处理结果直接保存为Excel文件。</p>

        <h2>7. 安全性设计</h2>
        <h3>7.1 文件安全</h3>
        <ul>
            <li><strong>文件格式验证：</strong>严格验证Excel文件格式，防止恶意文件</li>
            <li><strong>路径安全：</strong>限制文件访问路径，防止目录遍历攻击</li>
            <li><strong>内存管理：</strong>及时释放大文件占用的内存资源</li>
        </ul>

        <h3>7.2 数据安全</h3>
        <ul>
            <li><strong>本地处理：</strong>所有数据处理在本地完成，不上传到外部服务器</li>
            <li><strong>临时文件清理：</strong>处理完成后自动清理临时文件</li>
            <li><strong>错误处理：</strong>完善的异常处理机制，防止程序崩溃导致数据丢失</li>
        </ul>

        <h2>8. 性能优化</h2>
        <h3>8.1 处理性能</h3>
        <ul>
            <li><strong>多线程处理：</strong>数据处理操作在后台线程执行，避免界面卡顿</li>
            <li><strong>内存优化：</strong>使用Pandas的高效数据结构，优化内存使用</li>
            <li><strong>分块处理：</strong>对于大文件，采用分块读取和处理策略</li>
        </ul>

        <h3>8.2 界面性能</h3>
        <ul>
            <li><strong>异步更新：</strong>使用root.after()方法异步更新界面</li>
            <li><strong>进度反馈：</strong>实时显示处理进度，提升用户体验</li>
            <li><strong>响应式设计：</strong>界面支持窗口大小调整</li>
        </ul>

        <h2>9. 部署方案</h2>
        <h3>9.1 环境要求</h3>
        <table>
            <tr>
                <th>项目</th>
                <th>要求</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>操作系统</td>
                <td>Windows 7+, Linux, macOS</td>
                <td>跨平台支持</td>
            </tr>
            <tr>
                <td>Python版本</td>
                <td>3.7+</td>
                <td>支持现代Python特性</td>
            </tr>
            <tr>
                <td>内存</td>
                <td>2GB+</td>
                <td>处理大文件需要</td>
            </tr>
            <tr>
                <td>磁盘空间</td>
                <td>100MB+</td>
                <td>软件和依赖包</td>
            </tr>
        </table>

        <h3>9.2 安装部署</h3>
        <div class="code-block">
# 方式一：使用安装脚本
chmod +x install.sh
./install.sh

# 方式二：手动安装
python3 -m venv .venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate
pip install -r requirements.txt
python create_sample_data.py

# 启动软件
python run.py
        </div>

        <h2>10. 测试方案</h2>
        <h3>10.1 功能测试</h3>
        <ul>
            <li><strong>文件加载测试：</strong>测试各种格式和大小的Excel文件加载</li>
            <li><strong>对比功能测试：</strong>测试不同数据类型的列对比准确性</li>
            <li><strong>去重功能测试：</strong>验证去重算法的正确性和效率</li>
            <li><strong>格式调整测试：</strong>测试各种格式转换的准确性</li>
        </ul>

        <h3>10.2 性能测试</h3>
        <ul>
            <li><strong>大文件处理：</strong>测试处理50MB以上Excel文件的性能</li>
            <li><strong>内存使用：</strong>监控处理过程中的内存占用情况</li>
            <li><strong>响应时间：</strong>测试各功能的响应时间和处理速度</li>
        </ul>

        <h3>10.3 兼容性测试</h3>
        <ul>
            <li><strong>操作系统兼容性：</strong>在Windows、Linux、macOS上测试</li>
            <li><strong>Python版本兼容性：</strong>测试Python 3.7-3.11版本</li>
            <li><strong>Excel版本兼容性：</strong>测试.xls和.xlsx格式文件</li>
        </ul>

        <h2>11. 维护方案</h2>
        <h3>11.1 日志记录</h3>
        <ul>
            <li>记录文件处理操作日志</li>
            <li>记录错误和异常信息</li>
            <li>记录性能统计数据</li>
        </ul>

        <h3>11.2 版本管理</h3>
        <ul>
            <li>使用语义化版本号（Semantic Versioning）</li>
            <li>维护详细的更新日志</li>
            <li>提供版本回退机制</li>
        </ul>

        <h2>12. 扩展规划</h2>
        <h3>12.1 功能扩展</h3>
        <ul>
            <li><strong>数据拆分与合并：</strong>按列值拆分文件，多文件合并</li>
            <li><strong>快速报表生成：</strong>基于模板生成各类报表</li>
            <li><strong>数据统计分析：</strong>提供基础的数据统计功能</li>
            <li><strong>批量处理：</strong>支持批量处理多个文件</li>
        </ul>

        <h3>12.2 技术升级</h3>
        <ul>
            <li><strong>界面优化：</strong>采用现代化的GUI框架</li>
            <li><strong>性能提升：</strong>引入更高效的数据处理算法</li>
            <li><strong>云端集成：</strong>支持云存储文件处理</li>
        </ul>

        <script>
            // 初始化Mermaid
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                flowchart: {
                    useMaxWidth: true,
                    htmlLabels: true
                }
            });

            // 下载图表为PNG的函数
            function downloadDiagram(diagramId) {
                const element = document.getElementById(diagramId);
                const svg = element.querySelector('svg');

                if (svg) {
                    try {
                        // 克隆SVG并清理可能导致污染的属性
                        const clonedSvg = svg.cloneNode(true);

                        // 设置SVG的xmlns属性
                        clonedSvg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
                        clonedSvg.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');

                        // 获取SVG的尺寸
                        const bbox = svg.getBBox();
                        const width = bbox.width || 800;
                        const height = bbox.height || 600;

                        clonedSvg.setAttribute('width', width);
                        clonedSvg.setAttribute('height', height);
                        clonedSvg.setAttribute('viewBox', `0 0 ${width} ${height}`);

                        // 添加白色背景
                        const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
                        rect.setAttribute('width', '100%');
                        rect.setAttribute('height', '100%');
                        rect.setAttribute('fill', 'white');
                        clonedSvg.insertBefore(rect, clonedSvg.firstChild);

                        // 将SVG转换为字符串
                        const svgData = new XMLSerializer().serializeToString(clonedSvg);

                        // 创建canvas
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        canvas.width = width;
                        canvas.height = height;

                        // 创建Image对象
                        const img = new Image();
                        img.crossOrigin = 'anonymous'; // 设置跨域属性

                        img.onload = function() {
                            try {
                                // 填充白色背景
                                ctx.fillStyle = 'white';
                                ctx.fillRect(0, 0, canvas.width, canvas.height);

                                // 绘制SVG
                                ctx.drawImage(img, 0, 0);

                                // 下载PNG
                                canvas.toBlob(function(blob) {
                                    if (blob) {
                                        const url = URL.createObjectURL(blob);
                                        const a = document.createElement('a');
                                        a.href = url;
                                        a.download = diagramId + '.png';
                                        document.body.appendChild(a);
                                        a.click();
                                        document.body.removeChild(a);
                                        URL.revokeObjectURL(url);
                                    } else {
                                        // 如果toBlob失败，尝试使用toDataURL
                                        const dataUrl = canvas.toDataURL('image/png');
                                        const a = document.createElement('a');
                                        a.href = dataUrl;
                                        a.download = diagramId + '.png';
                                        document.body.appendChild(a);
                                        a.click();
                                        document.body.removeChild(a);
                                    }
                                }, 'image/png');

                            } catch (e) {
                                console.error('Canvas操作失败:', e);
                                // 降级方案：直接下载SVG
                                fallbackToSvg(diagramId, svgData);
                            }
                        };

                        img.onerror = function() {
                            console.error('图片加载失败');
                            // 降级方案：直接下载SVG
                            fallbackToSvg(diagramId, svgData);
                        };

                        // 使用data URL加载SVG
                        const svgDataUrl = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svgData);
                        img.src = svgDataUrl;

                    } catch (e) {
                        console.error('SVG处理失败:', e);
                        // 最终降级方案：下载原始SVG
                        const svgData = new XMLSerializer().serializeToString(svg);
                        fallbackToSvg(diagramId, svgData);
                    }
                }
            }

            // 直接下载SVG文件的函数
            function downloadSvg(diagramId, svgData) {
                const element = document.getElementById(diagramId);
                const svg = element.querySelector('svg');

                if (svg) {
                    // 如果没有提供svgData，则获取当前SVG
                    if (!svgData) {
                        const clonedSvg = svg.cloneNode(true);
                        clonedSvg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
                        clonedSvg.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');
                        svgData = new XMLSerializer().serializeToString(clonedSvg);
                    }

                    const blob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = diagramId + '.svg';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }
            }

            // 降级方案：当PNG导出失败时调用
            function fallbackToSvg(diagramId, svgData) {
                downloadSvg(diagramId, svgData);
                // 提示用户
                alert('PNG导出失败，已下载SVG格式。您可以使用在线工具或图像编辑软件将SVG转换为PNG。');
            }
        </script>
    </div>
</body>
</html>
