#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建示例Excel文件用于测试
"""

import pandas as pd
import numpy as np

def create_sample_files():
    """创建示例Excel文件"""
    
    # 创建文件A的数据
    data_a = {
        '姓名': ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'],
        '年龄': [25, 30, 35, 28, 32, 27, 29, 31],
        '部门': ['技术部', '销售部', '人事部', '技术部', '财务部', '销售部', '技术部', '人事部'],
        '工资': [8000, 7500, 6500, 8500, 7000, 7200, 8200, 6800],
        '入职日期': ['2020-01-15', '2019-03-20', '2018-07-10', '2021-02-28', '2019-11-05', '2020-06-12', '2021-01-08', '2018-12-03']
    }
    
    # 创建文件B的数据（与A有部分重叠，部分不同）
    data_b = {
        '姓名': ['张三', '李四', '王五', '赵六', '陈十一', '刘十二', '黄十三', '马十四'],
        '年龄': [25, 30, 35, 28, 26, 33, 29, 34],
        '部门': ['技术部', '销售部', '人事部', '技术部', '技术部', '财务部', '销售部', '人事部'],
        '工资': [8000, 7500, 6500, 8500, 7800, 7200, 6800, 6900],
        '入职日期': ['2020-01-15', '2019-03-20', '2018-07-10', '2021-02-28', '2021-03-15', '2019-08-22', '2020-09-30', '2019-05-18']
    }
    
    # 创建DataFrame
    df_a = pd.DataFrame(data_a)
    df_b = pd.DataFrame(data_b)
    
    # 保存为Excel文件
    df_a.to_excel('sample_file_a.xlsx', index=False)
    df_b.to_excel('sample_file_b.xlsx', index=False)
    
    print("示例文件创建完成：")
    print("- sample_file_a.xlsx")
    print("- sample_file_b.xlsx")
    print("\n文件A包含：", len(df_a), "条记录")
    print("文件B包含：", len(df_b), "条记录")
    print("\n共同数据：张三、李四、王五、赵六")
    print("A独有：钱七、孙八、周九、吴十")
    print("B独有：陈十一、刘十二、黄十三、马十四")

if __name__ == "__main__":
    create_sample_files()